/// 通用发布服务 - 为所有插件提供高性能Redis发布
/// 
/// 提供统一的发布接口，支持任意格式内容，专注于连接管理和性能优化
/// 各插件保持格式独立，通过此服务获得统一的高性能发布能力

use redis::{AsyncCommands, Client};
use std::sync::Arc;
use tokio::sync::RwLock;
use anyhow::Result;
use log::{debug, error, info};
use crate::core::types::get_config;

/// 通用发布服务接口
#[async_trait::async_trait]
pub trait UniversalPublisher: Send + Sync {
    /// 发布消息到指定频道
    async fn publish(&self, channel: &str, content: &str) -> Result<()>;

    /// 发布消息到多个频道
    async fn publish_multi(&self, channels: &[&str], content: &str) -> Result<()>;
}

/// Redis通用发布服务实现
pub struct RedisUniversalPublisher {
    /// Redis连接池
    connection_pool: Arc<RwLock<Vec<redis::aio::MultiplexedConnection>>>,
    /// 连接池大小
    pool_size: usize,
    /// Redis客户端
    client: Client,
}

impl RedisUniversalPublisher {
    /// 创建新的通用发布服务
    pub async fn new() -> Result<Self> {
        let config = get_config();
        let pool_size = config.redis.pool_size;
        
        info!("🚀 初始化通用发布服务，连接池大小: {}", pool_size);
        
        // 创建Redis客户端
        let client = Client::open(config.redis.url.clone())?;
        
        // 预创建连接池
        let mut connections = Vec::with_capacity(pool_size);
        for i in 0..pool_size {
            match client.get_multiplexed_async_connection().await {
                Ok(conn) => {
                    connections.push(conn);
                    debug!("✅ 通用发布服务连接 {}/{} 创建成功", i + 1, pool_size);
                }
                Err(e) => {
                    error!("❌ 通用发布服务连接 {}/{} 创建失败: {}", i + 1, pool_size, e);
                    return Err(e.into());
                }
            }
        }
        
        info!("✅ 通用发布服务初始化完成，连接池: {}/{}", connections.len(), pool_size);
        
        Ok(Self {
            connection_pool: Arc::new(RwLock::new(connections)),
            pool_size,
            client,
        })
    }
    
    /// 获取可用连接
    async fn get_connection(&self) -> Result<redis::aio::MultiplexedConnection> {
        let mut pool = self.connection_pool.write().await;
        
        if let Some(conn) = pool.pop() {
            // 返回连接前测试其可用性
            match redis::cmd("PING").query_async::<_, String>(&conn.clone()).await {
                Ok(_) => Ok(conn),
                Err(_) => {
                    // 连接失效，创建新连接
                    debug!("🔄 通用发布服务检测到失效连接，重新创建");
                    self.client.get_multiplexed_async_connection().await.map_err(Into::into)
                }
            }
        } else {
            // 连接池为空，创建新连接
            debug!("🔄 通用发布服务连接池为空，创建新连接");
            self.client.get_multiplexed_async_connection().await.map_err(Into::into)
        }
    }
    
    /// 归还连接到池中
    async fn return_connection(&self, conn: redis::aio::MultiplexedConnection) {
        let mut pool = self.connection_pool.write().await;
        if pool.len() < self.pool_size {
            pool.push(conn);
        }
        // 如果池已满，连接会被自动丢弃
    }
}

#[async_trait::async_trait]
impl UniversalPublisher for RedisUniversalPublisher {
    /// 发布消息到指定频道
    async fn publish(&self, channel: &str, content: &str) -> Result<()> {
        let start = std::time::Instant::now();
        
        // 获取连接
        let mut conn = self.get_connection().await?;
        
        // 执行发布
        let result: Result<i32, redis::RedisError> = conn.publish(channel, content).await;
        
        match result {
            Ok(subscriber_count) => {
                let duration = start.elapsed().as_nanos() as u64;
                debug!("📡 通用发布成功: 频道={}, 订阅者={}, 耗时={}ns", 
                      channel, subscriber_count, duration);
                
                // 归还连接
                self.return_connection(conn).await;
                Ok(())
            }
            Err(e) => {
                error!("❌ 通用发布失败: 频道={}, 错误={}", channel, e);
                // 发生错误时不归还连接，让其自动丢弃
                Err(e.into())
            }
        }
    }
    
    /// 发布消息到多个频道
    async fn publish_multi(&self, channels: &[&str], content: &str) -> Result<()> {
        let start = std::time::Instant::now();
        
        // 获取连接
        let mut conn = self.get_connection().await?;
        
        let mut total_subscribers = 0;
        let mut errors = Vec::new();
        
        // 并行发布到所有频道
        for channel in channels {
            match conn.publish(*channel, content).await {
                Ok(subscriber_count) => {
                    total_subscribers += subscriber_count;
                    debug!("📡 通用多频道发布成功: 频道={}, 订阅者={}", channel, subscriber_count);
                }
                Err(e) => {
                    error!("❌ 通用多频道发布失败: 频道={}, 错误={}", channel, e);
                    errors.push(format!("频道 {}: {}", channel, e));
                }
            }
        }
        
        let duration = start.elapsed().as_nanos() as u64;
        
        if errors.is_empty() {
            debug!("📡 通用多频道发布完成: 频道数={}, 总订阅者={}, 耗时={}ns", 
                  channels.len(), total_subscribers, duration);
            
            // 归还连接
            self.return_connection(conn).await;
            Ok(())
        } else {
            error!("❌ 通用多频道发布部分失败: 错误={:?}", errors);
            // 发生错误时不归还连接
            Err(anyhow::anyhow!("多频道发布失败: {}", errors.join(", ")))
        }
    }
}

/// 全局通用发布服务实例
static mut GLOBAL_PUBLISHER: Option<Arc<dyn UniversalPublisher>> = None;
static INIT: std::sync::Once = std::sync::Once::new();

/// 获取全局通用发布服务实例
pub async fn get_universal_publisher() -> Arc<dyn UniversalPublisher> {
    unsafe {
        INIT.call_once(|| {
            // 在异步上下文中初始化
            let rt = tokio::runtime::Handle::current();
            let publisher = rt.block_on(async {
                RedisUniversalPublisher::new().await
                    .expect("通用发布服务初始化失败")
            });
            GLOBAL_PUBLISHER = Some(Arc::new(publisher));
        });
        
        GLOBAL_PUBLISHER.as_ref().unwrap().clone()
    }
}

/// 便利函数：发布到单个频道
pub async fn publish_to_channel(channel: &str, content: &str) -> Result<()> {
    let publisher = get_universal_publisher().await;
    publisher.publish(channel, content).await
}

/// 便利函数：发布到多个频道
pub async fn publish_to_channels(channels: &[&str], content: &str) -> Result<()> {
    let publisher = get_universal_publisher().await;
    publisher.publish_multi(channels, content).await
}
