/// Bonk事件转换器
/// 
/// 负责将Bonk的LogTradeEvent转换为统一的ParsedEvent格式
/// 确保与现有Redis发布格式完全兼容

use std::time::Instant;
use compact_str::CompactString;
use crate::core::types::ParsedEvent;
use crate::plugins::bonk::models::trade_event::LogTradeEvent;
use crate::plugins::pump::models::events::{TradeEventExtended, TradeEvent};
use crate::plugins::pump::types::{PumpTransactionType, SwapDirection, TokenInfo, PriceInfo, FeeInfo, TransactionDetails};
use solana_program::pubkey::Pubkey;
use std::str::FromStr;
use rust_decimal::Decimal;

/// 将Bonk的LogTradeEvent转换为ParsedEvent
pub fn convert_bonk_to_parsed_event(
    bonk_event: &LogTradeEvent,
    publish_start: Instant,
) -> Result<ParsedEvent, Box<dyn std::error::Error + Send + Sync>> {
    // 创建TradeEventExtended以复用现有格式
    let extended_event = create_extended_trade_event_from_bonk(bonk_event)?;
    
    // 使用Bonk原有的文本格式
    let text_format = bonk_event.to_text_format();
    
    Ok(ParsedEvent {
        event: extended_event,
        json: CompactString::new(&text_format),
        timestamp: chrono::Utc::now().timestamp() as u64,
        publish_start,
    })
}

/// 从Bonk事件创建TradeEventExtended
fn create_extended_trade_event_from_bonk(
    bonk_event: &LogTradeEvent,
) -> Result<TradeEventExtended, Box<dyn std::error::Error + Send + Sync>> {
    // 解析mint地址
    let mint_pubkey = Pubkey::from_str(&bonk_event.mint_address)
        .map_err(|e| format!("无效的mint地址: {}", e))?;
    
    // 创建交易详情
    let details = TransactionDetails {
        signature: bonk_event.signature.clone(),
        transaction_type: PumpTransactionType::Buy, // Bonk主要是交易类型
        user: Some(Pubkey::from_str(&bonk_event.signer).unwrap_or_default()),
        token_info: Some(TokenInfo {
            mint: mint_pubkey,
            name: None,
            symbol: None,
            uri: None,
            decimals: 6, // 默认6位小数
            total_supply: None,
        }),
        swap_direction: Some(match bonk_event.trade_direction {
            crate::plugins::bonk::models::trade_event::TradeDirection::Buy => SwapDirection::Buy,
            crate::plugins::bonk::models::trade_event::TradeDirection::Sell => SwapDirection::Sell,
        }),
        sol_amount: Some(bonk_event.amount_in),
        token_amount: Some(bonk_event.amount_out),
        price_info: Some(PriceInfo {
            current_price: Some(Decimal::from_f64_retain(bonk_event.price_after).unwrap_or_default()),
            virtual_sol_reserves: bonk_event.virtual_quote,
            virtual_token_reserves: bonk_event.virtual_base,
            real_sol_reserves: Some(bonk_event.real_quote_after),
            real_token_reserves: Some(bonk_event.real_base_after),
        }),
        fee_info: Some(FeeInfo {
            fee_recipient: None,
            fee_basis_points: None,
            fee_amount: Some(bonk_event.protocol_fee),
            creator_fee_recipient: None,
            creator_fee_basis_points: None,
            creator_fee_amount: Some(bonk_event.platform_fee),
            creator_vault: None,
        }),
        timestamp: Some(chrono::Utc::now()),
        is_complete: true,
    };
    
    Ok(TradeEventExtended {
        event: create_trade_event_from_bonk(bonk_event)?,
        creator_vault: Pubkey::default(), // 暂时使用默认值
        signature: Some(bonk_event.signature.clone()),
        current_price: Some(bonk_event.price_after),
    })
}

/// 从Bonk事件创建TradeEvent
fn create_trade_event_from_bonk(
    bonk_event: &LogTradeEvent,
) -> Result<TradeEvent, Box<dyn std::error::Error + Send + Sync>> {
    // 解析mint地址
    let mint_pubkey = Pubkey::from_str(&bonk_event.mint_address)
        .map_err(|e| format!("无效的mint地址: {}", e))?;

    // 解析用户地址
    let user_pubkey = Pubkey::from_str(&bonk_event.signer)
        .map_err(|e| format!("无效的用户地址: {}", e))?;

    Ok(TradeEvent {
        mint: mint_pubkey,
        sol_amount: bonk_event.amount_in, // 使用amount_in作为SOL金额
        token_amount: bonk_event.amount_out, // 使用amount_out作为代币金额
        is_buy: matches!(bonk_event.trade_direction, crate::plugins::bonk::models::trade_event::TradeDirection::Buy),
        user: user_pubkey,
        timestamp: chrono::Utc::now().timestamp(),
        virtual_sol_reserves: bonk_event.virtual_quote,
        virtual_token_reserves: bonk_event.virtual_base,
        real_sol_reserves: bonk_event.real_quote_after,
        real_token_reserves: bonk_event.real_base_after,
        fee_recipient: Pubkey::default(), // 暂时使用默认值
        fee_basis_points: 0, // 暂时使用0
        fee: bonk_event.protocol_fee,
        creator: Pubkey::default(), // 暂时使用默认值
        creator_fee_basis_points: 0, // 暂时使用0
        creator_fee: bonk_event.platform_fee,
    })
}
