/// 统一Redis发布器
/// 
/// 提供高性能的统一发布接口，所有解析器共享使用
/// 使用连接池避免重复建连，实现零延迟发布

use anyhow::Result;
use log::{debug, error};
use once_cell::sync::Lazy;
use redis::{Client, aio::MultiplexedConnection};
use std::sync::Arc;
use tokio::sync::Mutex;
use crate::core::types::get_config;

/// 全局Redis连接池
static REDIS_CLIENT: Lazy<Arc<Client>> = Lazy::new(|| {
    let config = get_config();
    let client = Client::open(config.redis.url.clone())
        .expect("无法创建Redis客户端");
    Arc::new(client)
});

/// Redis连接管理器
pub struct UnifiedPublisher {
    connection: Arc<Mutex<Option<MultiplexedConnection>>>,
    channel: String,
}

impl UnifiedPublisher {
    /// 创建新的统一发布器实例
    pub fn new() -> Self {
        let config = get_config();
        Self {
            connection: Arc::new(Mutex::new(None)),
            channel: config.redis.channel.clone(),
        }
    }
    
    /// 获取或创建Redis连接
    async fn get_connection(&self) -> Result<MultiplexedConnection> {
        let mut conn_guard = self.connection.lock().await;
        
        if let Some(ref conn) = *conn_guard {
            // 尝试使用现有连接
            return Ok(conn.clone());
        }
        
        // 创建新连接
        match REDIS_CLIENT.get_multiplexed_async_connection().await {
            Ok(new_conn) => {
                debug!("创建新的Redis连接");
                *conn_guard = Some(new_conn.clone());
                Ok(new_conn)
            }
            Err(e) => {
                error!("创建Redis连接失败: {}", e);
                Err(e.into())
            }
        }
    }
    
    /// 发布内容到Redis频道
    pub async fn publish(&self, content: &str) -> Result<()> {
        let mut conn = self.get_connection().await?;
        
        match redis::cmd("PUBLISH")
            .arg(&self.channel)
            .arg(content)
            .query_async::<MultiplexedConnection, i64>(&mut conn)
            .await
        {
            Ok(_) => {
                debug!("成功发布到Redis频道: {}", self.channel);
                Ok(())
            }
            Err(e) => {
                error!("Redis发布失败: {}", e);
                // 连接可能已断开，清除缓存的连接
                *self.connection.lock().await = None;
                Err(e.into())
            }
        }
    }
}

/// 全局统一发布器实例
static GLOBAL_PUBLISHER: Lazy<UnifiedPublisher> = Lazy::new(|| {
    UnifiedPublisher::new()
});

/// 统一发布接口 - 所有解析器调用此函数
/// 
/// # 参数
/// * `content` - 要发布的内容字符串（任何格式）
/// 
/// # 返回
/// * `Result<()>` - 发布结果
pub async fn publish_to_redis(content: &str) -> Result<()> {
    GLOBAL_PUBLISHER.publish(content).await
}

/// 统一发布接口（带统计） - 包含性能统计
/// 
/// # 参数  
/// * `content` - 要发布的内容字符串
/// * `plugin_name` - 插件名称（用于统计）
/// 
/// # 返回
/// * `Result<()>` - 发布结果
pub async fn publish_to_redis_with_stats(content: &str, plugin_name: &str) -> Result<()> {
    use crate::core::stats::plugin_stats;
    use std::time::Instant;
    
    let start = Instant::now();
    
    match publish_to_redis(content).await {
        Ok(_) => {
            let elapsed = start.elapsed().as_nanos() as u64;
            plugin_stats::increment_published(plugin_name.to_string());
            plugin_stats::add_publish_time(plugin_name.to_string(), elapsed);
            debug!("{}发布成功 ({}ns)", plugin_name, elapsed);
            Ok(())
        }
        Err(e) => {
            plugin_stats::increment_publish_error(plugin_name.to_string());
            error!("{}发布失败: {}", plugin_name, e);
            Err(e)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_publish_to_redis() {
        // 注意：此测试需要Redis服务器运行
        let test_content = r#"test": "content""#;
        
        // 这里只测试函数调用，实际测试需要Redis环境
        // let result = publish_to_redis(test_content).await;
        // assert!(result.is_ok());
    }
}
