/// 统一Redis发布器池
///
/// 完全复制PublisherPool的高性能架构，支持任意格式内容发布
/// 使用多线程+预建连接+无锁队列实现零延迟发布

use std::sync::Arc;
use std::sync::atomic::Ordering;
use std::thread::{self, Jo<PERSON><PERSON><PERSON><PERSON>};
use std::time::Instant;

use crossbeam_channel::{Receiver, Sender, unbounded};
use log::{info, error};

use crate::core::types::{UnifiedEvent, RealTimeMetrics, PipelineConfig, get_config};
use crate::core::stats::plugin_stats;

/// 统一发布器池 - 完全复制PublisherPool架构
pub struct UnifiedPublisherPool {
    pub workers: Vec<JoinHandle<()>>,
    sender: Sender<UnifiedEvent>,
}

impl UnifiedPublisherPool {
    /// 创建新的统一发布器池
    pub fn new(
        config: PipelineConfig,
        metrics: Arc<RealTimeMetrics>,
    ) -> Self {
        let mut workers = Vec::new();
        let redis_url = &get_config().redis.url;

        // 创建无锁队列
        let (sender, receiver) = unbounded::<UnifiedEvent>();

        info!("🚀 启动 {} 个统一发布器工作线程...", config.publisher_threads);

        // 创建原子计数器跟踪连接状态
        let connected_count = Arc::new(std::sync::atomic::AtomicUsize::new(0));
        let total_threads = config.publisher_threads;

        for worker_id in 0..config.publisher_threads {
            let rx = receiver.clone();
            let metrics = Arc::clone(&metrics);
            let channel = config.redis_channel.clone();
            let redis_url = redis_url.clone();
            let connected_count = Arc::clone(&connected_count);

            let worker = thread::spawn(move || {
                let rt = tokio::runtime::Runtime::new().unwrap();

                rt.block_on(async {
                    // 启动带重试的Redis发布器
                    if let Err(e) = Self::run_publisher_with_retry(
                        worker_id,
                        &redis_url,
                        &channel,
                        &rx,
                        &metrics,
                        &connected_count,
                        total_threads,
                    ).await {
                        error!("统一发布器工作线程 {} 最终失败: {}", worker_id, e);
                    }
                });
            });

            workers.push(worker);
        }

        Self { workers, sender }
    }

    /// 获取发送端 - 供解析器使用
    pub fn get_sender(&self) -> Sender<UnifiedEvent> {
        self.sender.clone()
    }

    /// 运行发布器，带自动重试机制 - 完全复制PublisherPool逻辑
    async fn run_publisher_with_retry(
        worker_id: usize,
        redis_url: &str,
        channel: &str,
        receiver: &Receiver<UnifiedEvent>,
        metrics: &RealTimeMetrics,
        connected_count: &std::sync::atomic::AtomicUsize,
        total_threads: usize,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let mut retry_count = 0;
        let max_retries = 10;
        let retry_delay = std::time::Duration::from_secs(2);

        loop {
            // 只在第一次连接和失败重试时打印
            if retry_count > 0 {
                info!("🚀 统一发布器工作线程 {} 尝试连接Redis (重试 {}/{})", worker_id, retry_count, max_retries);
            }

            match Self::run_publisher(worker_id, redis_url, channel, receiver, metrics, connected_count, total_threads).await {
                Ok(_) => {
                    info!("✅ 统一发布器工作线程 {} 正常退出", worker_id);
                    break;
                }
                Err(e) => {
                    retry_count += 1;
                    error!("❌ 统一发布器工作线程 {} 连接失败: {} (重试 {}/{})",
                        worker_id, e, retry_count, max_retries);

                    if retry_count >= max_retries {
                        error!("🚨 统一发布器工作线程 {} 达到最大重试次数", worker_id);
                        return Err(e);
                    }

                    tokio::time::sleep(retry_delay).await;
                }
            }
        }

        Ok(())
    }

    /// 运行单个发布器 - 完全复制PublisherPool逻辑
    async fn run_publisher(
        worker_id: usize,
        redis_url: &str,
        channel: &str,
        receiver: &Receiver<UnifiedEvent>,
        metrics: &RealTimeMetrics,
        connected_count: &std::sync::atomic::AtomicUsize,
        total_threads: usize,
    ) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // 创建Redis连接
        let client = redis::Client::open(redis_url.to_string())?;
        let mut conn = client.get_multiplexed_async_connection().await?;

        // 增加连接计数并检查是否所有工作线程都已连接
        let current_count = connected_count.fetch_add(1, Ordering::SeqCst) + 1;
        if current_count == total_threads {
            info!("✅ 所有统一发布器工作线程已连接到Redis");
        }

        loop {
            match receiver.recv() {
                Ok(event) => {
                    let start = Instant::now();

                    // 立即发布 - 无等待
                    match redis::cmd("PUBLISH")
                        .arg(channel)
                        .arg(event.content.as_str())
                        .query_async::<redis::aio::MultiplexedConnection, i64>(&mut conn)
                        .await
                    {
                        Ok(_) => {
                            let duration = start.elapsed().as_nanos() as u64;
                            metrics.publish_time_total.fetch_add(duration, Ordering::Relaxed);
                            metrics.published_count.fetch_add(1, Ordering::Relaxed);

                            // 更新插件统计
                            plugin_stats::increment_published(event.plugin_name.clone());
                            plugin_stats::add_publish_time(event.plugin_name, duration);
                        }
                        Err(e) => {
                            metrics.publish_errors.fetch_add(1, Ordering::Relaxed);
                            error!("Redis发布失败: {}", e);

                            // Redis错误时退出，让上层重试
                            return Err(e.into());
                        }
                    }
                }
                Err(_) => {
                    info!("统一发布器工作线程 {} 接收通道关闭", worker_id);
                    break;
                }
            }
        }

        Ok(())
    }
}

/// 便捷发布函数 - 供解析器直接调用
///
/// # 参数
/// * `content` - 要发布的内容字符串
/// * `plugin_name` - 插件名称（用于统计）
///
/// # 返回
/// * `Result<()>` - 发布结果
pub fn send_to_unified_publisher(
    sender: &Sender<UnifiedEvent>,
    content: String,
    plugin_name: String,
) -> Result<(), crossbeam_channel::SendError<UnifiedEvent>> {
    use compact_str::CompactString;

    let event = UnifiedEvent {
        content: CompactString::new(&content),
        timestamp: chrono::Utc::now().timestamp() as u64,
        publish_start: Instant::now(),
        plugin_name,
    };

    sender.send(event)
}
